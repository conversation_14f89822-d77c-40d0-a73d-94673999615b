{"expo": {"name": "antiscam", "slug": "antiscam", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "antiscam", "userInterfaceStyle": "automatic", "newArchEnabled": true, "backgroundColor": "#000", "ios": {"supportsTablet": true, "backgroundColor": "#000"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#000"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}