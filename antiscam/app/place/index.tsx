import Ionicons from "@expo/vector-icons/Ionicons";
import { LinearGradient } from "expo-linear-gradient";
import React from "react";
import {
  Image,
  Pressable,
  ScrollView,
  StatusBar,
  Text,
  View,
} from "react-native";

export default function Index() {
  const [active, setActive] = React.useState(0);
  const [showMap, setShowMap] = React.useState(false);

  return (
    <ScrollView className="flex-1 pb-60">
      <StatusBar barStyle="light-content" />
      <View className="flex-1">
        <View className="relative">
          <LinearGradient
            colors={["rgba(0,0,0,0.99)", "transparent", "rgba(0,0,0,0.99)"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            style={{
              position: "absolute",
              left: 0,
              bottom: 0,
              width: "100%",
              height: "100%",
              zIndex: 10,
            }}
          />
          <Image
            source={{
              uri: "https://images.pexels.com/photos/338936/pexels-photo-338936.jpeg?cs=srgb&dl=pexels-nejc-kosir-108379-338936.jpg&fm=jpg",
            }}
            className="w-full aspect-[5/4]"
          />
        </View>

        <View className="px-5">
          <View>
            <Text className="text-3xl text-gray-200 mb-2">Hyde Park</Text>
            <View className="flex-row items-center gap-1">
              <Ionicons name="location" size={20} color="white" />
              <Text className="text-gray-200">
                Unit 1, The serpertine , South Carriage Drive
              </Text>
            </View>
            <View className="mt-4 flex-row justify-between">
              <Pressable
                className="bg-gray-800 px-4 py-2 rounded-lg flex-row items-center"
                onPress={() => setShowMap(!showMap)}
              >
                <Ionicons
                  name="map-outline"
                  size={18}
                  color="white"
                  className="mr-2"
                />
                <Text className="text-white">
                  {showMap ? "Hide Map" : "Show Map"}
                </Text>
              </Pressable>
              <Pressable className="bg-blue-800 px-4 py-2 rounded-lg flex-row items-center">
                <Ionicons
                  name="navigate-outline"
                  size={18}
                  color="white"
                  className="mr-2"
                />
                <Text className="text-white">Directions</Text>
              </Pressable>
            </View>

            {showMap && (
              <View className="mt-4 bg-gray-800 h-48 rounded-lg items-center justify-center">
                <Ionicons name="map" size={40} color="gray" />
                <Text className="text-gray-400 mt-2">Map View</Text>
              </View>
            )}
          </View>
          <View className="mt-5 flex-row gap-2">
            <View className="flex-row w-full items-center justify-between">
              <View className="flex-row items-center gap-2">
                <View className="flex-row items-center">
                  {[
                    "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?q=80&w=2960&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                    "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                    "https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=2960&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                    "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?q=80&w=2960&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                    "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                  ].map((image, key) => (
                    <Image
                      key={key}
                      source={{ uri: image }}
                      className="w-8 h-8 rounded-full -ml-2 bg-gray-400 shadow border-2 border-black"
                    />
                  ))}
                </View>
                <Text className="text-gray-400">+155 reviews</Text>
              </View>
              <View className="flex-row items-center gap-2">
                <Ionicons name="star" size={20} color="yellow" />
                <View className="flex-row gap-1">
                  <Text className="text-white font-bold text-sm">4.1</Text>
                  <Text className="text-gray-400 text-sm">/ 1.5 km</Text>
                </View>
              </View>
            </View>
          </View>

          <View className="mt-10">
            <View className="w-full flex-row justify-between">
              <Pressable
                className={[
                  "flex-1 p-2 rounded-lg",
                  active === 0 && "bg-gray-200",
                ].join(" ")}
                onPress={() => setActive(0)}
              >
                <Text
                  className={[
                    "text-center font-semibold",
                    active === 0 ? "text-black" : "text-gray-400",
                  ].join(" ")}
                >
                  About
                </Text>
              </Pressable>
              <Pressable
                className={[
                  "flex-1 p-2 rounded-lg",
                  active === 1 && "bg-gray-200",
                ].join(" ")}
                onPress={() => setActive(1)}
              >
                <Text
                  className={[
                    "text-center font-semibold",
                    active === 1 ? "text-black" : "text-gray-400",
                  ].join(" ")}
                >
                  Forecast
                </Text>
              </Pressable>
              <Pressable
                className={[
                  "flex-1 p-2 rounded-lg",
                  active === 2 && "bg-gray-200",
                ].join(" ")}
                onPress={() => setActive(2)}
              >
                <Text
                  className={[
                    "text-center font-semibold",
                    active === 2 ? "text-black" : "text-gray-400",
                  ].join(" ")}
                >
                  Packing
                </Text>
              </Pressable>
            </View>

            {active === 0 && (
              <View className="mt-5 rounded-lg">
                <Text className="text-white font-bold text-lg">About</Text>
                <Text className="text-gray-300 mt-2">
                  Hyde Park is one of London's largest and most famous parks.
                  Covering 350 acres, it offers various recreational activities
                  including boating on the Serpentine lake, horse riding, and
                  open water swimming.
                </Text>
                <Text className="text-gray-300 mt-2">
                  The park hosts major events throughout the year, including
                  concerts, Winter Wonderland, and various cultural
                  celebrations.
                </Text>

                <View className="mt-5 space-y-3">
                  <Text className="text-white font-bold mb-2">Facilities</Text>
                  <View className="flex-row flex-wrap gap-3">
                    {[
                      { icon: "restaurant", text: "Cafés" },
                      { icon: "water", text: "Fountains" },
                      { icon: "bicycle", text: "Bike Rental" },
                      { icon: "boat", text: "Boating" },
                    ].map((item, i) => (
                      <View key={i} className="items-center w-16">
                        <View className="bg-gray-700 p-2 rounded-full mb-1">
                          <Ionicons name={item.icon} size={20} color="white" />
                        </View>
                        <Text className="text-gray-300 text-xs text-center">
                          {item.text}
                        </Text>
                      </View>
                    ))}
                  </View>
                </View>

                <View className="mt-4 flex-row flex-wrap gap-2">
                  {[
                    "Open 24/7",
                    "Free Entry",
                    "Pet Friendly",
                    "Picnic Areas",
                  ].map((tag, i) => (
                    <View
                      key={i}
                      className="bg-gray-800 px-3 py-1 rounded-full"
                    >
                      <Text className="text-gray-300 text-xs">{tag}</Text>
                    </View>
                  ))}
                </View>

                <View className="mt-5 space-y-3">
                  <Text className="text-white font-bold">Opening Hours</Text>
                  <View className="bg-gray-800/50 p-3 rounded-lg">
                    <Text className="text-gray-200">
                      Park: 5:00 AM - 12:00 AM
                    </Text>
                    <Text className="text-gray-200">
                      Cafés: 9:00 AM - 6:00 PM
                    </Text>
                    <Text className="text-gray-200">
                      Boating: 10:00 AM - 8:00 PM (Summer)
                    </Text>
                  </View>
                </View>
              </View>
            )}
            {active === 1 && (
              <View className="mt-5 rounded-lg">
                <Text className="text-white font-bold text-lg">Forecast</Text>

                <View className="mt-4 bg-gray-800/50 p-4 rounded-lg">
                  <Text className="text-gray-400">Current Weather</Text>
                  <View className="flex-row items-center justify-between mt-2">
                    <View className="flex-row items-center">
                      <Ionicons name="sunny" size={40} color="yellow" />
                      <View className="ml-3">
                        <Text className="text-white text-3xl font-bold">
                          24°C
                        </Text>
                        <Text className="text-gray-400">Feels like 26°C</Text>
                      </View>
                    </View>
                    <View>
                      <Text className="text-gray-300">Wind: 8 km/h</Text>
                      <Text className="text-gray-300">Humidity: 45%</Text>
                    </View>
                  </View>
                </View>

                <View className="mt-4 space-y-3">
                  {[
                    {
                      day: "Today",
                      temp: "24°C",
                      icon: "sunny",
                      high: "26°C",
                      low: "18°C",
                    },
                    {
                      day: "Tomorrow",
                      temp: "22°C",
                      icon: "partly-sunny",
                      high: "23°C",
                      low: "17°C",
                    },
                    {
                      day: "Wednesday",
                      temp: "19°C",
                      icon: "rainy",
                      high: "20°C",
                      low: "15°C",
                    },
                    {
                      day: "Thursday",
                      temp: "21°C",
                      icon: "partly-sunny",
                      high: "22°C",
                      low: "16°C",
                    },
                    {
                      day: "Friday",
                      temp: "23°C",
                      icon: "sunny",
                      high: "25°C",
                      low: "18°C",
                    },
                  ].map((item, i) => (
                    <View
                      key={i}
                      className="flex-row items-center justify-between bg-gray-800/50 p-3 rounded-lg"
                    >
                      <Text className="text-gray-200 w-24">{item.day}</Text>
                      <View className="flex-row items-center gap-2">
                        <Ionicons name={item.icon} size={20} color="white" />
                        <Text className="text-gray-200">{item.temp}</Text>
                      </View>
                      <View className="flex-row items-center gap-2">
                        <Text className="text-gray-200">H: {item.high}</Text>
                        <Text className="text-gray-400">L: {item.low}</Text>
                      </View>
                    </View>
                  ))}
                </View>

                <Pressable className="mt-5 border border-gray-700 p-3 rounded-lg">
                  <Text className="text-white text-center">
                    View Full Forecast
                  </Text>
                </Pressable>
              </View>
            )}
            {active === 2 && (
              <View className="mt-5 rounded-lg">
                <Text className="text-white font-bold text-lg">Packing</Text>

                <View className="mt-3 flex-row justify-between">
                  <Text className="text-gray-300">
                    Suggested items for your visit
                  </Text>
                  <Pressable>
                    <Text className="text-blue-400">Select All</Text>
                  </Pressable>
                </View>

                <View className="mt-4 space-y-3">
                  <Text className="text-gray-400 font-medium">Essentials</Text>
                  {[
                    { item: "Water Bottle", essential: true, icon: "water" },
                    { item: "Sunscreen", essential: true, icon: "sunny" },
                    { item: "Hat", essential: true, icon: "shirt" },
                    {
                      item: "Comfortable Shoes",
                      essential: true,
                      icon: "footsteps",
                    },
                  ].map((item, i) => (
                    <View
                      key={i}
                      className="flex-row items-center justify-between bg-gray-800/50 p-3 rounded-lg"
                    >
                      <View className="flex-row items-center gap-2">
                        <Ionicons name={item.icon} size={20} color="white" />
                        <Text className="text-gray-200">{item.item}</Text>
                      </View>
                      <View className="bg-green-900/50 px-2 py-1 rounded">
                        <Text className="text-green-400 text-xs">
                          Essential
                        </Text>
                      </View>
                    </View>
                  ))}
                </View>

                <View className="mt-4 space-y-3">
                  <Text className="text-gray-400 font-medium">Optional</Text>
                  {[
                    { item: "Picnic Blanket", essential: false, icon: "bed" },
                    { item: "Camera", essential: false, icon: "camera" },
                    { item: "Frisbee", essential: false, icon: "disc" },
                    { item: "Binoculars", essential: false, icon: "search" },
                  ].map((item, i) => (
                    <View
                      key={i}
                      className="flex-row items-center justify-between bg-gray-800/50 p-3 rounded-lg"
                    >
                      <View className="flex-row items-center gap-2">
                        <Ionicons name={item.icon} size={20} color="white" />
                        <Text className="text-gray-200">{item.item}</Text>
                      </View>
                    </View>
                  ))}
                </View>

                <Pressable className="mt-5 bg-blue-800 p-3 rounded-lg">
                  <Text className="text-white text-center font-semibold">
                    Download Packing List
                  </Text>
                </Pressable>
              </View>
            )}
          </View>
        </View>
      </View>
    </ScrollView>
  );
}
