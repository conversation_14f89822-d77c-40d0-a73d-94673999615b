import { Pressable, Text, TextInput, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function Login() {
  return (
    <SafeAreaView className="flex-1 bg-black">
      <View className="flex-1 justify-center pt-20">
        <View className="items-center">
          <View className="w-20 h-20 rounded-full bg-blue-400 mb-4" />
          <Text className="text-white text-3xl font-black">Welcome back</Text>
          <Text className=" text-lg text-gray-200 font-medium">
            Login to your account, and start building
          </Text>
        </View>
        <View className="mt-10 gap-2">
          <View className="gap-2">
            <Text className="text-white text-lg font-semibold">Email</Text>
            <TextInput
              className="bg-white p-4 rounded-lg placeholder:font-medium placeholder:text-gray-400"
              placeholder="Email"
            />
          </View>
          <View className="gap-2">
            <Text className="text-white text-lg font-semibold">Password</Text>
            <TextInput
              className="bg-white p-4 rounded-lg placeholder:font-medium placeholder:text-gray-400"
              placeholder="Password"
            />
          </View>
          <View className="mt-4">
            <Pressable className="bg-blue-400 p-4 rounded-lg">
              <Text className=" text-center font-bold text-white">Login</Text>
            </Pressable>
          </View>
          <View className="mt-4">
            <Text className="text-white text-center">
              Forgot your password?
            </Text>
          </View>
          <View className="mt-4">
            <Text className="text-white text-center">
              Don't have an account?{" "}
              <Text className="text-blue-400">Sign up</Text>
            </Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}
